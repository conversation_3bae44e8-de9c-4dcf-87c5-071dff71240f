import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ServicesModule } from '@shared/services/services.module';
import { APP_INTERCEPTOR } from '@nestjs/core';
import {
  UserProduct,
  UserClassification,
  CustomField,
  PhysicalWarehouse,
  UserOrder,
  VirtualWarehouse,
  Warehouse,
  Inventory,
  UserConvert,
  UserConvertCustomer,
  UserAddress,

  CustomerFacebook,
  CustomerWeb,
  File,
  Folder
} from '../entities';
import { Product } from '@modules/marketplace/entities';
import { Agent } from '@modules/agent/entities/agent.entity';

import {
  UserProductRepository,
  UserOrderRepository,
  WarehouseRepository,
  PhysicalWarehouseRepository,
  VirtualWarehouseRepository,
  InventoryRepository,
  UserClassificationRepository,
  CustomFieldRepository,
  UserConvertRepository,
  UserConvertCustomerRepository,
  UserAddressRepository,

  CustomerFacebookRepository,
  CustomerWebRepository,
  FileRepository,
  FolderRepository,
  BusinessReportRepository
} from '../repositories';
import { ProductRepository } from '@modules/marketplace/repositories';

import {
  UserProductController,
  UserOrderController,
  UserWarehouseController,
  UserInventoryController,
  UserPhysicalWarehouseController,
  CustomFieldController,
  BusinessIntegrationController,
  ClassificationController,
  UserConvertController,
  UserConvertCustomerController,
  UserFileController,
  UserFolderController,
  UserVirtualWarehouseController,
  BusinessReportController,
} from './controllers';
import { GHTKShipmentController } from './controllers/ghtk-shipment.controller';

import {
  UserProductService,
  UserOrderService,
  UserWarehouseService,
  UserInventoryService,
  UserPhysicalWarehouseService,
  CustomFieldService,
  BusinessIntegrationService,
  ClassificationService,
  UserConvertService,
  UserConvertCustomerService,
  UserFileService,
  UserFolderService,
  UserVirtualWarehouseService,
  BusinessReportService,
} from './services';
import { GHTKShipmentService } from './services/ghtk-shipment.service';

import { ValidationHelper, UserProductHelper, BusinessReportHelper, MetadataHelper } from './helpers';
import { BusinessIntegrationInterceptor } from './interceptors/business-integration.interceptor';

/**
 * Module quản lý chức năng business cho người dùng
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserProduct,
      UserClassification,
      CustomField,
      PhysicalWarehouse,
      UserOrder,
      VirtualWarehouse,
      Warehouse,
      Inventory,
      UserConvert,
      UserConvertCustomer,
      UserAddress,

      CustomerFacebook,
      CustomerWeb,
      Product,
      Agent,
      File,
      Folder
    ]),
    ServicesModule
  ],
  controllers: [
    UserProductController,
    UserOrderController,
    UserWarehouseController,
    UserInventoryController,
    UserPhysicalWarehouseController,
    CustomFieldController,
    BusinessIntegrationController,
    ClassificationController,
    UserConvertController,
    UserConvertCustomerController,
    UserFileController,
    UserFolderController,
    UserVirtualWarehouseController,
    BusinessReportController,
    GHTKShipmentController
  ],
  providers: [
    // Repositories
    UserProductRepository,
    UserOrderRepository,
    WarehouseRepository,
    PhysicalWarehouseRepository,
    VirtualWarehouseRepository,
    InventoryRepository,
    UserClassificationRepository,
    CustomFieldRepository,
    UserConvertRepository,
    UserConvertCustomerRepository,
    UserAddressRepository,

    CustomerFacebookRepository,
    CustomerWebRepository,
    ProductRepository,
    FileRepository,
    FolderRepository,
    BusinessReportRepository,

    // Helpers
    ValidationHelper,
    UserProductHelper,
    BusinessReportHelper,
    MetadataHelper,

    // Services
    UserProductService,
    UserOrderService,
    UserWarehouseService,
    UserInventoryService,
    UserPhysicalWarehouseService,
    CustomFieldService,
    ClassificationService,
    BusinessIntegrationService,
    UserConvertService,
    UserConvertCustomerService,
    UserFileService,
    UserFolderService,
    UserVirtualWarehouseService,
    BusinessReportService,
    GHTKShipmentService,

    // Interceptors
    {
      provide: APP_INTERCEPTOR,
      useClass: BusinessIntegrationInterceptor
    }
  ],
  exports: [
    TypeOrmModule,
    UserProductService,
    UserOrderService,
    UserWarehouseService,
    UserInventoryService,
    UserPhysicalWarehouseService,
    CustomFieldService,
    ClassificationService,
    BusinessIntegrationService,
    UserConvertService,
    UserConvertCustomerService,
    UserFileService,
    UserFolderService,
    UserVirtualWarehouseService,
    BusinessReportService,
    GHTKShipmentService,
    UserProductRepository,
  ],
})
export class BusinessUserModule {}
