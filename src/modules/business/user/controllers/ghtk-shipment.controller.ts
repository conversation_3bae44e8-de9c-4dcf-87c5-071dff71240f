import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  Query,
  UseGuards,
  Logger,
  Res,
  HttpStatus
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { Response } from 'express';
import { JwtAuthGuard } from '@modules/auth/guards/jwt-auth.guard';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { User } from '@modules/auth/entities/user.entity';
import { GHTKShipmentService } from '../services/ghtk-shipment.service';
import { GHTKConfigValidationHelper } from '../helpers/ghtk-config-validation.helper';
import {
  CreateGHTKOrderRequestDto,
  CalculateGHTKFeeRequestDto,
  GetGHTKOrderStatusRequestDto,
  PrintGHTKLabelRequestDto,
  CancelGHTKOrderRequestDto,
  GetGHTKLevel4AddressRequestDto,
  SearchGHTKProductRequestDto,
  CreateGHTKOrderResponseDto,
  CalculateGHTKFeeResponseDto,
  GetGHTKOrderStatusResponseDto,
  CancelGHTKOrderResponseDto,
  GetGHTKPickupAddressesResponseDto,
  GetGHTKLevel4AddressResponseDto,
  SearchGHTKProductResponseDto,
  GetGHTKSolutionsResponseDto,
  GHTKWebhookDataDto
} from '../dto/ghtk';

/**
 * Controller xử lý các API GHTK
 */
@ApiTags('GHTK Shipment')
@Controller('ghtk')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class GHTKShipmentController {
  private readonly logger = new Logger(GHTKShipmentController.name);

  constructor(
    private readonly ghtkService: GHTKShipmentService,
    private readonly configValidationHelper: GHTKConfigValidationHelper
  ) {}

  /**
   * Kiểm tra cấu hình GHTK
   */
  @Get('config/validate')
  @ApiOperation({
    summary: 'Kiểm tra cấu hình GHTK',
    description: 'Validate cấu hình GHTK và trả về báo cáo chi tiết'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Kiểm tra cấu hình thành công',
    schema: {
      type: 'object',
      properties: {
        isValid: { type: 'boolean' },
        errors: { type: 'array', items: { type: 'string' } },
        warnings: { type: 'array', items: { type: 'string' } },
        environment: { type: 'string' },
        report: { type: 'string' }
      }
    }
  })
  async validateConfig(@CurrentUser() user: User) {
    this.logger.log(`User ${user.id} kiểm tra cấu hình GHTK`);

    const validation = this.configValidationHelper.validateGHTKConfig();
    const envInfo = this.configValidationHelper.getEnvironmentInfo();
    const compatibility = this.configValidationHelper.validateEnvironmentCompatibility();
    const report = this.configValidationHelper.generateConfigReport();

    return {
      isValid: validation.isValid,
      errors: validation.errors,
      warnings: validation.warnings,
      environment: envInfo.nodeEnv,
      isTestMode: validation.config.isTestMode,
      compatibility: compatibility,
      report: report
    };
  }

  /**
   * Lấy danh sách giải pháp GHTK
   */
  @Get('solutions')
  @ApiOperation({
    summary: 'Lấy danh sách giải pháp GHTK',
    description: 'Trả về danh sách các giải pháp (gói bảo hiểm/phụ phí) mà shop có thể áp dụng'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách giải pháp thành công',
    type: GetGHTKSolutionsResponseDto
  })
  async getSolutions(@CurrentUser() user: User): Promise<GetGHTKSolutionsResponseDto> {
    this.logger.log(`User ${user.id} lấy danh sách giải pháp GHTK`);
    return await this.ghtkService.getSolutions();
  }

  /**
   * Tạo đơn hàng GHTK
   */
  @Post('orders')
  @ApiOperation({
    summary: 'Tạo đơn hàng GHTK',
    description: 'Gửi thông tin đơn hàng lên hệ thống GHTK để tạo vận đơn'
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Tạo đơn hàng GHTK thành công',
    type: CreateGHTKOrderResponseDto
  })
  async createOrder(
    @CurrentUser() user: User,
    @Body() createOrderDto: CreateGHTKOrderRequestDto
  ): Promise<CreateGHTKOrderResponseDto> {
    this.logger.log(`User ${user.id} tạo đơn hàng GHTK: ${createOrderDto.order.id}`);
    return await this.ghtkService.createOrder(createOrderDto);
  }

  /**
   * Tính phí vận chuyển GHTK
   */
  @Get('calculate-fee')
  @ApiOperation({
    summary: 'Tính phí vận chuyển GHTK',
    description: 'Tính toán chi phí vận chuyển và các phụ phí dựa trên thông tin địa chỉ, khối lượng, giá trị hàng'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Tính phí vận chuyển thành công',
    type: CalculateGHTKFeeResponseDto
  })
  async calculateFee(
    @CurrentUser() user: User,
    @Query() calculateFeeDto: CalculateGHTKFeeRequestDto
  ): Promise<CalculateGHTKFeeResponseDto> {
    this.logger.log(`User ${user.id} tính phí vận chuyển GHTK từ ${calculateFeeDto.pickProvince} đến ${calculateFeeDto.province}`);
    return await this.ghtkService.calculateFee(calculateFeeDto);
  }

  /**
   * Lấy trạng thái đơn hàng GHTK
   */
  @Get('orders/:trackingOrder/status')
  @ApiOperation({
    summary: 'Lấy trạng thái đơn hàng GHTK',
    description: 'Lấy thông tin trạng thái hiện tại của một đơn hàng đã được GHTK nhận'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy trạng thái đơn hàng thành công',
    type: GetGHTKOrderStatusResponseDto
  })
  async getOrderStatus(
    @CurrentUser() user: User,
    @Param('trackingOrder') trackingOrder: string
  ): Promise<GetGHTKOrderStatusResponseDto> {
    this.logger.log(`User ${user.id} lấy trạng thái đơn hàng GHTK: ${trackingOrder}`);
    return await this.ghtkService.getOrderStatus(trackingOrder);
  }

  /**
   * Hủy đơn hàng GHTK
   */
  @Post('orders/:trackingOrder/cancel')
  @ApiOperation({
    summary: 'Hủy đơn hàng GHTK',
    description: 'Hủy đơn hàng đã được gửi lên GHTK (chỉ áp dụng với các đơn ở trạng thái có thể hủy)'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Hủy đơn hàng thành công',
    type: CancelGHTKOrderResponseDto
  })
  async cancelOrder(
    @CurrentUser() user: User,
    @Param('trackingOrder') trackingOrder: string
  ): Promise<CancelGHTKOrderResponseDto> {
    this.logger.log(`User ${user.id} hủy đơn hàng GHTK: ${trackingOrder}`);
    return await this.ghtkService.cancelOrder(trackingOrder);
  }

  /**
   * In nhãn đơn hàng GHTK
   */
  @Get('orders/:trackingOrder/label')
  @ApiOperation({
    summary: 'In nhãn đơn hàng GHTK',
    description: 'Trả về file PDF chứa nhãn dán lên kiện hàng của đơn'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'In nhãn đơn hàng thành công',
    content: {
      'application/pdf': {
        schema: {
          type: 'string',
          format: 'binary'
        }
      }
    }
  })
  async printLabel(
    @CurrentUser() user: User,
    @Param('trackingOrder') trackingOrder: string,
    @Query() printOptions: PrintGHTKLabelRequestDto,
    @Res() res: Response
  ): Promise<void> {
    this.logger.log(`User ${user.id} in nhãn đơn hàng GHTK: ${trackingOrder}`);
    
    const pdfBuffer = await this.ghtkService.printLabel(trackingOrder, printOptions);
    
    res.set({
      'Content-Type': 'application/pdf',
      'Content-Disposition': `attachment; filename="ghtk-label-${trackingOrder}.pdf"`,
      'Content-Length': pdfBuffer.length.toString()
    });
    
    res.send(pdfBuffer);
  }

  /**
   * Lấy danh sách địa chỉ lấy hàng GHTK
   */
  @Get('pickup-addresses')
  @ApiOperation({
    summary: 'Lấy danh sách địa chỉ lấy hàng GHTK',
    description: 'Trả về danh sách các địa chỉ kho hàng (điểm lấy hàng) đã được shop cài đặt trên hệ thống GHTK'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách địa chỉ lấy hàng thành công',
    type: GetGHTKPickupAddressesResponseDto
  })
  async getPickupAddresses(@CurrentUser() user: User): Promise<GetGHTKPickupAddressesResponseDto> {
    this.logger.log(`User ${user.id} lấy danh sách địa chỉ lấy hàng GHTK`);
    return await this.ghtkService.getPickupAddresses();
  }

  /**
   * Lấy danh sách địa chỉ cấp 4 GHTK
   */
  @Get('level4-addresses')
  @ApiOperation({
    summary: 'Lấy danh sách địa chỉ cấp 4 GHTK',
    description: 'Với một địa chỉ cụ thể, trả về danh sách các địa chỉ cấp 4 (tên toà nhà, xóm, ngõ,...) trong khu vực đó'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách địa chỉ cấp 4 thành công',
    type: GetGHTKLevel4AddressResponseDto
  })
  async getLevel4Address(
    @CurrentUser() user: User,
    @Query() addressQuery: GetGHTKLevel4AddressRequestDto
  ): Promise<GetGHTKLevel4AddressResponseDto> {
    this.logger.log(`User ${user.id} lấy danh sách địa chỉ cấp 4 GHTK cho ${addressQuery.province}, ${addressQuery.district}`);
    return await this.ghtkService.getLevel4Address(addressQuery);
  }

  /**
   * Tìm kiếm sản phẩm GHTK
   */
  @Get('products/search')
  @ApiOperation({
    summary: 'Tìm kiếm sản phẩm GHTK',
    description: 'Tìm kiếm và trả về danh sách sản phẩm mà shop đã tạo/đăng lên GHTK'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Tìm kiếm sản phẩm thành công',
    type: SearchGHTKProductResponseDto
  })
  async searchProducts(
    @CurrentUser() user: User,
    @Query() searchQuery: SearchGHTKProductRequestDto
  ): Promise<SearchGHTKProductResponseDto> {
    this.logger.log(`User ${user.id} tìm kiếm sản phẩm GHTK: ${searchQuery.term}`);
    return await this.ghtkService.searchProducts(searchQuery.term);
  }

  /**
   * Webhook endpoint để nhận cập nhật từ GHTK
   * Endpoint này không cần authentication vì được gọi từ GHTK
   */
  @Post('webhook')
  @ApiOperation({
    summary: 'Webhook từ GHTK',
    description: 'Endpoint để nhận thông báo cập nhật trạng thái đơn hàng từ GHTK'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xử lý webhook thành công'
  })
  async handleWebhook(@Body() webhookData: GHTKWebhookDataDto): Promise<{ success: boolean }> {
    this.logger.log(`Nhận webhook từ GHTK cho đơn hàng: ${webhookData.labelId}`);
    
    await this.ghtkService.handleWebhook(webhookData);
    
    return { success: true };
  }
}
