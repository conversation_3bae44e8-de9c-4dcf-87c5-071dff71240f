import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { GHTKConfigValidationHelper } from '../helpers/ghtk-config-validation.helper';
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import {
  GHTK_BASE_URLS,
  GHTK_ENDPOINTS,
  GHTK_DEFAULT_HEADERS,
  GHTK_TEST_CONFIG,
  GHTK_TIMEOUT,
  GHTK_ERROR_MESSAGES
} from '@modules/business/constants/ghtk.constants';
import {
  IGHTKConfig,
  IGHTKService,
  IGHTKCreateOrderRequest,
  IGHTKCreateOrderResponse,
  IGHTKCalculateFeeResponse,
  IGHTKOrderStatusResponse,
  IGHTKCancelOrderResponse,
  IGHTKPickupAddressesResponse,
  IGHTKLevel4AddressResponse,
  IGHTKSearchProductResponse,
  IGHTKSolutionsResponse,
  IGHTKWebhookData
} from '@modules/business/interfaces/ghtk.interface';
import {
  CreateGHTKOrderRequestDto,
  CalculateGHTKFeeRequestDto,
  GetGHTKOrderStatusRequestDto,
  PrintGHTKLabelRequestDto,
  CancelGHTKOrderRequestDto,
  GetGHTKLevel4AddressRequestDto,
  SearchGHTKProductRequestDto
} from '../dto/ghtk';

/**
 * Service xử lý tất cả các tính năng vận chuyển GHTK
 */
@Injectable()
export class GHTKShipmentService implements IGHTKService {
  private readonly logger = new Logger(GHTKShipmentService.name);
  private config: IGHTKConfig;
  private httpClient: AxiosInstance;

  constructor(
    private readonly configService: ConfigService,
    private readonly configValidationHelper: GHTKConfigValidationHelper
  ) {
    // Validate cấu hình trước khi khởi tạo
    const validation = this.configValidationHelper.validateGHTKConfig();

    if (!validation.isValid) {
      this.logger.error('GHTK configuration validation failed:', validation.errors);
      // Log warnings nhưng vẫn tiếp tục với cấu hình mặc định
      if (validation.warnings.length > 0) {
        this.logger.warn('GHTK configuration warnings:', validation.warnings);
      }
    }

    // Sử dụng cấu hình đã được validate
    this.config = validation.config;

    this.initializeHttpClient();

    this.logger.log('GHTK Service initialized', {
      baseUrl: this.config.baseUrl,
      isTestMode: this.config.isTestMode,
      hasToken: !!this.config.token,
      hasPartnerCode: !!this.config.partnerCode,
      validationPassed: validation.isValid,
      warningsCount: validation.warnings.length
    });

    // Log configuration report in debug mode
    if (this.configService.get<string>('LOG_LEVEL') === 'debug') {
      this.logger.debug('GHTK Configuration Report:\n' + this.configValidationHelper.generateConfigReport());
    }
  }

  /**
   * Khởi tạo HTTP client với cấu hình
   */
  private initializeHttpClient(): void {
    this.httpClient = axios.create({
      baseURL: this.config.baseUrl,
      timeout: this.config.timeout,
      headers: {
        ...GHTK_DEFAULT_HEADERS,
        'Token': this.config.token,
        ...(this.config.partnerCode && { 'X-Client-Source': this.config.partnerCode })
      }
    });

    // Request interceptor
    this.httpClient.interceptors.request.use(
      (config) => {
        this.logger.debug(`GHTK API Request: ${config.method?.toUpperCase()} ${config.url}`, {
          headers: config.headers,
          params: config.params,
          data: config.data
        });
        return config;
      },
      (error) => {
        this.logger.error('GHTK API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.httpClient.interceptors.response.use(
      (response) => {
        this.logger.debug(`GHTK API Response: ${response.status}`, {
          data: response.data
        });
        return response;
      },
      (error) => {
        this.logger.error('GHTK API Response Error:', {
          status: error.response?.status,
          data: error.response?.data,
          message: error.message
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * Thiết lập cấu hình GHTK
   */
  setConfig(config: IGHTKConfig): void {
    this.config = { ...this.config, ...config };
    this.initializeHttpClient();
    this.logger.log('GHTK configuration updated', {
      baseUrl: this.config.baseUrl,
      isTestMode: this.config.isTestMode
    });
  }

  /**
   * Lấy cấu hình hiện tại
   */
  getConfig(): IGHTKConfig {
    return { ...this.config };
  }

  /**
   * Lấy danh sách giải pháp GHTK
   */
  async getSolutions(): Promise<IGHTKSolutionsResponse> {
    try {
      this.logger.log('Lấy danh sách giải pháp GHTK');

      const response: AxiosResponse<IGHTKSolutionsResponse> = await this.httpClient.get(
        GHTK_ENDPOINTS.GET_SOLUTIONS
      );

      if (!response.data.success) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHTK_API_ERROR,
          response.data.message || GHTK_ERROR_MESSAGES.API_ERROR
        );
      }

      this.logger.log(`Lấy thành công ${response.data.data?.length || 0} giải pháp GHTK`);
      return response.data;
    } catch (error) {
      this.logger.error('Lỗi khi lấy danh sách giải pháp GHTK:', error);
      this.handleGHTKError(error);
    }
  }

  /**
   * Tạo đơn hàng GHTK
   */
  async createOrder(request: IGHTKCreateOrderRequest): Promise<IGHTKCreateOrderResponse> {
    try {
      this.logger.log('Tạo đơn hàng GHTK', { orderId: request.order.id });

      const response: AxiosResponse<IGHTKCreateOrderResponse> = await this.httpClient.post(
        GHTK_ENDPOINTS.CREATE_ORDER,
        request
      );

      if (!response.data.success) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHTK_API_ERROR,
          response.data.message || GHTK_ERROR_MESSAGES.API_ERROR
        );
      }

      this.logger.log('Tạo đơn hàng GHTK thành công', {
        orderId: request.order.id,
        label: response.data.order.label,
        trackingId: response.data.order.tracking_id
      });

      return response.data;
    } catch (error) {
      this.logger.error('Lỗi khi tạo đơn hàng GHTK:', error);
      this.handleGHTKError(error);
    }
  }

  /**
   * Tính phí vận chuyển GHTK
   */
  async calculateFee(params: CalculateGHTKFeeRequestDto): Promise<IGHTKCalculateFeeResponse> {
    try {
      this.logger.log('Tính phí vận chuyển GHTK', {
        from: `${params.pickProvince}, ${params.pickDistrict}`,
        to: `${params.province}, ${params.district}`,
        weight: params.weight
      });

      // Chuyển đổi params sang format GHTK API
      const queryParams = {
        pick_address_id: params.pickAddressId,
        pick_address: params.pickAddress,
        pick_province: params.pickProvince,
        pick_district: params.pickDistrict,
        pick_ward: params.pickWard,
        pick_street: params.pickStreet,
        address: params.address,
        province: params.province,
        district: params.district,
        ward: params.ward,
        street: params.street,
        weight: params.weight,
        value: params.value,
        transport: params.transport,
        deliver_option: params.deliverOption,
        tags: params.tags
      };

      const response: AxiosResponse<IGHTKCalculateFeeResponse> = await this.httpClient.get(
        GHTK_ENDPOINTS.CALCULATE_FEE,
        { params: queryParams }
      );

      if (!response.data.success) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHTK_API_ERROR,
          response.data.message || GHTK_ERROR_MESSAGES.API_ERROR
        );
      }

      this.logger.log('Tính phí vận chuyển GHTK thành công', {
        fee: response.data.fee.fee,
        insuranceFee: response.data.fee.insurance_fee,
        delivery: response.data.fee.delivery
      });

      return response.data;
    } catch (error) {
      this.logger.error('Lỗi khi tính phí vận chuyển GHTK:', error);
      this.handleGHTKError(error);
    }
  }

  /**
   * Lấy trạng thái đơn hàng GHTK
   */
  async getOrderStatus(trackingOrder: string): Promise<IGHTKOrderStatusResponse> {
    try {
      this.logger.log('Lấy trạng thái đơn hàng GHTK', { trackingOrder });

      const response: AxiosResponse<IGHTKOrderStatusResponse> = await this.httpClient.get(
        `${GHTK_ENDPOINTS.GET_ORDER_STATUS}/${trackingOrder}`
      );

      if (!response.data.success) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHTK_API_ERROR,
          response.data.message || GHTK_ERROR_MESSAGES.ORDER_NOT_FOUND
        );
      }

      this.logger.log('Lấy trạng thái đơn hàng GHTK thành công', {
        trackingOrder,
        status: response.data.order.status_text,
        statusId: response.data.order.status_id
      });

      return response.data;
    } catch (error) {
      this.logger.error('Lỗi khi lấy trạng thái đơn hàng GHTK:', error);
      this.handleGHTKError(error);
    }
  }

  /**
   * Hủy đơn hàng GHTK
   */
  async cancelOrder(trackingOrder: string): Promise<IGHTKCancelOrderResponse> {
    try {
      this.logger.log('Hủy đơn hàng GHTK', { trackingOrder });

      const response: AxiosResponse<IGHTKCancelOrderResponse> = await this.httpClient.post(
        `${GHTK_ENDPOINTS.CANCEL_ORDER}/${trackingOrder}`
      );

      if (!response.data.success) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHTK_API_ERROR,
          response.data.message || GHTK_ERROR_MESSAGES.CANNOT_CANCEL
        );
      }

      this.logger.log('Hủy đơn hàng GHTK thành công', {
        trackingOrder,
        logId: response.data.log_id
      });

      return response.data;
    } catch (error) {
      this.logger.error('Lỗi khi hủy đơn hàng GHTK:', error);
      this.handleGHTKError(error);
    }
  }

  /**
   * In nhãn đơn hàng GHTK
   */
  async printLabel(trackingOrder: string, options?: PrintGHTKLabelRequestDto): Promise<Buffer> {
    try {
      this.logger.log('In nhãn đơn hàng GHTK', { trackingOrder, options });

      const queryParams: any = {};
      if (options?.original) queryParams.original = options.original;
      if (options?.paperSize) queryParams.paper_size = options.paperSize;

      const response: AxiosResponse<Buffer> = await this.httpClient.get(
        `${GHTK_ENDPOINTS.PRINT_LABEL}/${trackingOrder}`,
        {
          params: queryParams,
          responseType: 'arraybuffer'
        }
      );

      this.logger.log('In nhãn đơn hàng GHTK thành công', {
        trackingOrder,
        size: response.data.length
      });

      return response.data;
    } catch (error) {
      this.logger.error('Lỗi khi in nhãn đơn hàng GHTK:', error);
      this.handleGHTKError(error);
    }
  }

  /**
   * Lấy danh sách địa chỉ lấy hàng GHTK
   */
  async getPickupAddresses(): Promise<IGHTKPickupAddressesResponse> {
    try {
      this.logger.log('Lấy danh sách địa chỉ lấy hàng GHTK');

      const response: AxiosResponse<IGHTKPickupAddressesResponse> = await this.httpClient.get(
        GHTK_ENDPOINTS.GET_PICKUP_ADDRESSES
      );

      if (!response.data.success) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHTK_API_ERROR,
          response.data.message || GHTK_ERROR_MESSAGES.API_ERROR
        );
      }

      this.logger.log(`Lấy thành công ${response.data.data?.length || 0} địa chỉ lấy hàng GHTK`);
      return response.data;
    } catch (error) {
      this.logger.error('Lỗi khi lấy danh sách địa chỉ lấy hàng GHTK:', error);
      this.handleGHTKError(error);
    }
  }

  /**
   * Lấy danh sách địa chỉ cấp 4 GHTK
   */
  async getLevel4Address(params: GetGHTKLevel4AddressRequestDto): Promise<IGHTKLevel4AddressResponse> {
    try {
      this.logger.log('Lấy danh sách địa chỉ cấp 4 GHTK', params);

      const queryParams = {
        province: params.province,
        district: params.district,
        ward_street: params.wardStreet,
        address: params.address
      };

      const response: AxiosResponse<IGHTKLevel4AddressResponse> = await this.httpClient.get(
        GHTK_ENDPOINTS.GET_LEVEL4_ADDRESS,
        { params: queryParams }
      );

      if (!response.data.success) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHTK_API_ERROR,
          response.data.message || GHTK_ERROR_MESSAGES.API_ERROR
        );
      }

      this.logger.log(`Lấy thành công ${response.data.data?.length || 0} địa chỉ cấp 4 GHTK`);
      return response.data;
    } catch (error) {
      this.logger.error('Lỗi khi lấy danh sách địa chỉ cấp 4 GHTK:', error);
      this.handleGHTKError(error);
    }
  }

  /**
   * Tìm kiếm sản phẩm GHTK
   */
  async searchProducts(term: string): Promise<IGHTKSearchProductResponse> {
    try {
      this.logger.log('Tìm kiếm sản phẩm GHTK', { term });

      const response: AxiosResponse<IGHTKSearchProductResponse> = await this.httpClient.get(
        GHTK_ENDPOINTS.SEARCH_PRODUCTS,
        { params: { term } }
      );

      if (!response.data.success) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHTK_API_ERROR,
          response.data.message || GHTK_ERROR_MESSAGES.API_ERROR
        );
      }

      this.logger.log(`Tìm thấy ${response.data.data?.length || 0} sản phẩm GHTK`);
      return response.data;
    } catch (error) {
      this.logger.error('Lỗi khi tìm kiếm sản phẩm GHTK:', error);
      this.handleGHTKError(error);
    }
  }

  /**
   * Xử lý webhook từ GHTK
   */
  async handleWebhook(data: IGHTKWebhookData): Promise<void> {
    try {
      this.logger.log('Xử lý webhook từ GHTK', {
        partnerId: data.partner_id,
        labelId: data.label_id,
        statusId: data.status_id
      });

      // TODO: Implement webhook handling logic
      // - Cập nhật trạng thái đơn hàng trong database
      // - Gửi thông báo cho user
      // - Log lịch sử thay đổi trạng thái

      this.logger.log('Xử lý webhook GHTK thành công');
    } catch (error) {
      this.logger.error('Lỗi khi xử lý webhook GHTK:', error);
      throw error;
    }
  }

  /**
   * Xử lý lỗi từ GHTK API
   */
  private handleGHTKError(error: any): never {
    if (error instanceof AppException) {
      throw error;
    }

    if (error.code === 'ECONNABORTED' || error.code === 'ETIMEDOUT') {
      throw new AppException(
        BUSINESS_ERROR_CODES.GHTK_TIMEOUT_ERROR,
        GHTK_ERROR_MESSAGES.TIMEOUT_ERROR
      );
    }

    if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      throw new AppException(
        BUSINESS_ERROR_CODES.GHTK_NETWORK_ERROR,
        GHTK_ERROR_MESSAGES.NETWORK_ERROR
      );
    }

    if (error.response?.status === 401) {
      throw new AppException(
        BUSINESS_ERROR_CODES.GHTK_INVALID_TOKEN,
        GHTK_ERROR_MESSAGES.INVALID_TOKEN
      );
    }

    if (error.response?.status === 404) {
      throw new AppException(
        BUSINESS_ERROR_CODES.GHTK_ORDER_NOT_FOUND,
        GHTK_ERROR_MESSAGES.ORDER_NOT_FOUND
      );
    }

    // Lỗi chung
    const message = error.response?.data?.message || error.message || GHTK_ERROR_MESSAGES.API_ERROR;
    throw new AppException(
      BUSINESS_ERROR_CODES.GHTK_API_ERROR,
      message
    );
  }
}
