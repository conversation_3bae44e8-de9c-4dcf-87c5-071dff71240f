# GHTK Service Usage Examples

## Tổng quan

GHTK Service cung cấp đầy đủ các tính năng để tích hợp với API GHTK bao gồm:

- ✅ Lấy danh sách giải pháp
- ✅ Tạo đơn hàng vận chuyển
- ✅ Tính phí vận chuyển
- ✅ Lấy trạng thái đơn hàng
- ✅ Hủy đơn hàng
- ✅ In nhãn đơn hàng
- ✅ Lấy danh sách địa chỉ lấy hàng
- ✅ Lấy địa chỉ cấp 4
- ✅ Tìm kiếm sản phẩm
- ✅ Xử lý webhook

## Cấu hình

Service sử dụng token test mặc định từ tài liệu GHTK:

```typescript
const GHTK_TEST_CONFIG = {
  TOKEN: 'APITokenSample-ca441e70288cB0515F310742',
  PARTNER_CODE: 'PARTNER_CODE_SAMPLE',
  BASE_URL: 'https://services.ghtk.vn'
};
```

## API Endpoints

### 1. L<PERSON>y danh sách giải pháp
```http
GET /api/v1/user/ghtk/solutions
Authorization: Bearer <jwt_token>
```

### 2. Tạo đơn hàng GHTK
```http
POST /api/v1/user/ghtk/orders
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "products": [
    {
      "name": "Laptop Asus",
      "weight": 2.5,
      "quantity": 1,
      "price": 8000000
    }
  ],
  "order": {
    "id": "ORDER_123456",
    "pickName": "Kho HCM nội thành",
    "pickAddress": "590 CMT8 P.11",
    "pickProvince": "TP. Hồ Chí Minh",
    "pickDistrict": "Quận 3",
    "pickWard": "Phường 1",
    "pickTel": "0911222333",
    "name": "Nguyễn Văn A",
    "address": "123 Nguyễn Chí Thanh",
    "province": "TP. Hồ Chí Minh",
    "district": "Quận 1",
    "ward": "Phường Bến Nghé",
    "tel": "0987654321",
    "value": 8000000
  }
}
```

### 3. Tính phí vận chuyển
```http
GET /api/v1/user/ghtk/calculate-fee?pickProvince=Hà Nội&pickDistrict=Quận Hai Bà Trưng&province=Hà Nội&district=Quận Cầu Giấy&weight=1000&deliverOption=none
Authorization: Bearer <jwt_token>
```

### 4. Lấy trạng thái đơn hàng
```http
GET /api/v1/user/ghtk/orders/S1.A1.17373471/status
Authorization: Bearer <jwt_token>
```

### 5. Hủy đơn hàng
```http
POST /api/v1/user/ghtk/orders/S1.A1.17373471/cancel
Authorization: Bearer <jwt_token>
```

### 6. In nhãn đơn hàng
```http
GET /api/v1/user/ghtk/orders/S1.A1.2001297581/label?original=portrait&paperSize=A6
Authorization: Bearer <jwt_token>
```

### 7. Lấy danh sách địa chỉ lấy hàng
```http
GET /api/v1/user/ghtk/pickup-addresses
Authorization: Bearer <jwt_token>
```

### 8. Lấy địa chỉ cấp 4
```http
GET /api/v1/user/ghtk/level4-addresses?province=Hà Nội&district=Quận Ba Đình&wardStreet=Đội Cấn
Authorization: Bearer <jwt_token>
```

### 9. Tìm kiếm sản phẩm
```http
GET /api/v1/user/ghtk/products/search?term=laptop
Authorization: Bearer <jwt_token>
```

### 10. Webhook (không cần authentication)
```http
POST /api/v1/user/ghtk/webhook
Content-Type: application/json

{
  "partnerId": "1234567",
  "labelId": "S1.A1.17373471",
  "statusId": 5,
  "actionTime": "2016-11-02T12:18:39+07:00",
  "reasonCode": "",
  "reason": "",
  "weight": 2.4,
  "fee": 15000,
  "pickMoney": 100000,
  "returnPartPackage": 0
}
```

## Sử dụng trong code

### Inject service
```typescript
import { GHTKShipmentService } from '../services/ghtk-shipment.service';

@Injectable()
export class YourService {
  constructor(private readonly ghtkService: GHTKShipmentService) {}
}
```

### Tạo đơn hàng
```typescript
const orderRequest = {
  products: [
    {
      name: "Laptop Asus",
      weight: 2.5,
      quantity: 1,
      price: 8000000
    }
  ],
  order: {
    id: "ORDER_123456",
    // ... other order info
  }
};

const result = await this.ghtkService.createOrder(orderRequest);
console.log('GHTK Label:', result.order.label);
```

### Tính phí vận chuyển
```typescript
const feeRequest = {
  pickProvince: "Hà Nội",
  pickDistrict: "Quận Hai Bà Trưng",
  province: "Hà Nội",
  district: "Quận Cầu Giấy",
  weight: 1000,
  deliverOption: "none"
};

const feeResult = await this.ghtkService.calculateFee(feeRequest);
console.log('Shipping Fee:', feeResult.fee.fee);
```

## Tích hợp với UserOrderService

Sử dụng `GHTKIntegrationHelper` để tích hợp với hệ thống đơn hàng:

```typescript
import { GHTKIntegrationHelper } from '../helpers/ghtk-integration.helper';

// Tạo đơn hàng GHTK từ đơn hàng hệ thống
const ghtkOrder = await this.ghtkHelper.createGHTKOrder(
  order,
  customer,
  products,
  pickupInfo
);

// Tính phí vận chuyển
const shippingFee = await this.ghtkHelper.calculateGHTKShippingFee(
  order,
  customer,
  products,
  pickupInfo
);
```

## Error Handling

Service xử lý các loại lỗi sau:

- `GHTK_API_ERROR`: Lỗi chung từ API GHTK
- `GHTK_INVALID_TOKEN`: Token không hợp lệ
- `GHTK_ORDER_NOT_FOUND`: Không tìm thấy đơn hàng
- `GHTK_CANNOT_CANCEL`: Không thể hủy đơn hàng
- `GHTK_NETWORK_ERROR`: Lỗi kết nối mạng
- `GHTK_TIMEOUT_ERROR`: Timeout khi gọi API

## Logging

Service ghi log chi tiết cho tất cả các hoạt động:

```
[GHTKShipmentService] Tạo đơn hàng GHTK { orderId: 'ORDER_123456' }
[GHTKShipmentService] Tạo đơn hàng GHTK thành công { orderId: 'ORDER_123456', label: 'S1.A1.2001297581', trackingId: 2001297581 }
```

## Test với Postman

1. Import collection từ file `ghtk-api.postman_collection.json`
2. Thiết lập environment variables:
   - `base_url`: `http://localhost:3000/api/v1/user`
   - `jwt_token`: Token JWT của user
3. Chạy các request theo thứ tự trong collection

## Lưu ý quan trọng

1. **Test Mode**: Service hiện đang sử dụng token test, phù hợp cho development
2. **Production**: Cần cập nhật token và partner code thực tế khi deploy production
3. **Webhook**: Endpoint webhook không cần authentication, cần bảo mật bằng cách khác (IP whitelist, signature verification)
4. **Rate Limiting**: GHTK có giới hạn số request, cần implement rate limiting nếu cần
5. **Error Recovery**: Implement retry logic cho các API quan trọng như tạo đơn hàng
