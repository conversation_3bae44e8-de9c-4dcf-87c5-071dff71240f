import { Injectable, Logger } from '@nestjs/common';
import { GHTKShipmentService } from '../services/ghtk-shipment.service';
import { UserOrder } from '../../entities/user-order.entity';
import { UserProduct } from '../../entities/user-product.entity';
import { UserConvertCustomer } from '../../entities/user-convert-customer.entity';
import { IGHTKCreateOrderRequest, IGHTKProduct, IGHTKOrder } from '../../interfaces/ghtk.interface';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '../../exceptions';

/**
 * Helper để tích hợp GHTK với hệ thống đơn hàng
 */
@Injectable()
export class GHTKIntegrationHelper {
  private readonly logger = new Logger(GHTKIntegrationHelper.name);

  constructor(private readonly ghtkService: GHTKShipmentService) {}

  /**
   * Chuyển đổi đơn hàng hệ thống sang format GHTK
   * @param order Đơn hàng hệ thống
   * @param customer Thông tin khách hàng
   * @param products Danh sách sản phẩm
   * @param pickupInfo Thông tin địa chỉ lấy hàng
   * @returns Request data cho GHTK API
   */
  convertOrderToGHTKFormat(
    order: UserOrder,
    customer: UserConvertCustomer,
    products: UserProduct[],
    pickupInfo: {
      name: string;
      address: string;
      province: string;
      district: string;
      ward: string;
      tel: string;
    }
  ): IGHTKCreateOrderRequest {
    try {
      // Chuyển đổi sản phẩm
      const ghtkProducts: IGHTKProduct[] = this.convertProductsToGHTKFormat(order, products);

      // Chuyển đổi thông tin đơn hàng
      const ghtkOrder: IGHTKOrder = this.convertOrderInfoToGHTKFormat(
        order,
        customer,
        pickupInfo
      );

      return {
        products: ghtkProducts,
        order: ghtkOrder
      };
    } catch (error) {
      this.logger.error('Lỗi khi chuyển đổi đơn hàng sang format GHTK:', error);
      throw new AppException(
        BUSINESS_ERROR_CODES.GHTK_CONFIG_ERROR,
        `Lỗi khi chuyển đổi đơn hàng: ${error.message}`
      );
    }
  }

  /**
   * Chuyển đổi danh sách sản phẩm sang format GHTK
   */
  private convertProductsToGHTKFormat(order: UserOrder, products: UserProduct[]): IGHTKProduct[] {
    const orderProducts = order.productInfo?.products || [];
    
    return orderProducts.map((orderProduct: any) => {
      const product = products.find(p => p.id === orderProduct.productId);
      
      if (!product) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          `Không tìm thấy sản phẩm với ID ${orderProduct.productId}`
        );
      }

      // Lấy thông tin cân nặng từ shipmentConfig
      const weight = product.shipmentConfig?.weightGram 
        ? product.shipmentConfig.weightGram / 1000 // Chuyển từ gram sang kg
        : 0.5; // Mặc định 0.5kg nếu không có thông tin

      return {
        name: orderProduct.name || product.name,
        price: orderProduct.unitPrice || 0,
        weight: weight,
        quantity: orderProduct.quantity || 1,
        product_code: product.id.toString() // Sử dụng ID sản phẩm làm product_code
      };
    });
  }

  /**
   * Chuyển đổi thông tin đơn hàng sang format GHTK
   */
  private convertOrderInfoToGHTKFormat(
    order: UserOrder,
    customer: UserConvertCustomer,
    pickupInfo: any
  ): IGHTKOrder {
    // Parse địa chỉ giao hàng
    const deliveryAddress = this.parseDeliveryAddress(order.logisticInfo?.deliveryAddress || customer.address);
    
    // Tính tổng giá trị đơn hàng
    const orderValue = this.calculateOrderValue(order);

    return {
      id: `ORDER_${order.id}_${Date.now()}`, // Tạo ID unique cho GHTK
      
      // Thông tin lấy hàng
      pick_name: pickupInfo.name,
      pick_address: pickupInfo.address,
      pick_province: pickupInfo.province,
      pick_district: pickupInfo.district,
      pick_ward: pickupInfo.ward,
      pick_tel: pickupInfo.tel,
      
      // Thông tin giao hàng
      name: customer.name,
      address: deliveryAddress.address,
      province: deliveryAddress.province,
      district: deliveryAddress.district,
      ward: deliveryAddress.ward,
      hamlet: deliveryAddress.hamlet || 'Khác',
      tel: customer.phone,
      
      // Thông tin đơn hàng
      value: orderValue,
      pick_money: order.billInfo?.total || 0, // COD amount
      note: order.logisticInfo?.shippingNote || '',
      transport: 'road', // Mặc định đường bộ
      pick_option: 'cod', // Mặc định thu hộ
      deliver_option: 'none', // Không sử dụng xTeam
      is_freeship: '0' // Mặc định không freeship
    };
  }

  /**
   * Parse địa chỉ giao hàng từ string
   */
  private parseDeliveryAddress(addressString: string): {
    address: string;
    province: string;
    district: string;
    ward: string;
    hamlet?: string;
  } {
    // Tách địa chỉ theo dấu phẩy
    const parts = addressString.split(',').map(part => part.trim());
    
    if (parts.length < 3) {
      throw new AppException(
        BUSINESS_ERROR_CODES.GHTK_INVALID_ADDRESS,
        'Địa chỉ giao hàng không đủ thông tin (cần ít nhất: địa chỉ, quận/huyện, tỉnh/thành)'
      );
    }

    return {
      address: parts[0] || '',
      ward: parts[1] || '',
      district: parts[2] || '',
      province: parts[3] || parts[2], // Nếu không có ward thì district là parts[1], province là parts[2]
      hamlet: 'Khác'
    };
  }

  /**
   * Tính tổng giá trị đơn hàng
   */
  private calculateOrderValue(order: UserOrder): number {
    const products = order.productInfo?.products || [];
    return products.reduce((total: number, product: any) => {
      return total + (product.totalPrice || 0);
    }, 0);
  }

  /**
   * Tạo đơn hàng GHTK từ đơn hàng hệ thống
   * @param order Đơn hàng hệ thống
   * @param customer Thông tin khách hàng
   * @param products Danh sách sản phẩm
   * @param pickupInfo Thông tin địa chỉ lấy hàng
   * @returns Thông tin đơn hàng GHTK đã tạo
   */
  async createGHTKOrder(
    order: UserOrder,
    customer: UserConvertCustomer,
    products: UserProduct[],
    pickupInfo: any
  ) {
    try {
      this.logger.log(`Tạo đơn hàng GHTK cho order ID: ${order.id}`);

      // Chuyển đổi sang format GHTK
      const ghtkRequest = this.convertOrderToGHTKFormat(order, customer, products, pickupInfo);

      // Gọi API GHTK
      const ghtkResponse = await this.ghtkService.createOrder(ghtkRequest);

      this.logger.log(`Tạo đơn hàng GHTK thành công:`, {
        orderId: order.id,
        ghtkLabel: ghtkResponse.order.label,
        trackingId: ghtkResponse.order.tracking_id
      });

      return ghtkResponse;
    } catch (error) {
      this.logger.error(`Lỗi khi tạo đơn hàng GHTK cho order ${order.id}:`, error);
      throw error;
    }
  }

  /**
   * Tính phí vận chuyển GHTK
   * @param order Đơn hàng hệ thống
   * @param customer Thông tin khách hàng
   * @param products Danh sách sản phẩm
   * @param pickupInfo Thông tin địa chỉ lấy hàng
   * @returns Thông tin phí vận chuyển
   */
  async calculateGHTKShippingFee(
    order: UserOrder,
    customer: UserConvertCustomer,
    products: UserProduct[],
    pickupInfo: any
  ) {
    try {
      this.logger.log(`Tính phí vận chuyển GHTK cho order ID: ${order.id}`);

      // Parse địa chỉ
      const deliveryAddress = this.parseDeliveryAddress(order.logisticInfo?.deliveryAddress || customer.address);
      
      // Tính tổng trọng lượng
      const totalWeight = this.calculateTotalWeight(order, products);
      
      // Tính tổng giá trị
      const orderValue = this.calculateOrderValue(order);

      // Gọi API tính phí GHTK
      const feeResponse = await this.ghtkService.calculateFee({
        pickProvince: pickupInfo.province,
        pickDistrict: pickupInfo.district,
        pickWard: pickupInfo.ward,
        province: deliveryAddress.province,
        district: deliveryAddress.district,
        ward: deliveryAddress.ward,
        weight: Math.ceil(totalWeight * 1000), // Chuyển sang gram và làm tròn lên
        value: orderValue,
        deliverOption: 'none'
      });

      this.logger.log(`Tính phí vận chuyển GHTK thành công:`, {
        orderId: order.id,
        fee: feeResponse.fee.fee,
        insuranceFee: feeResponse.fee.insurance_fee
      });

      return feeResponse;
    } catch (error) {
      this.logger.error(`Lỗi khi tính phí vận chuyển GHTK cho order ${order.id}:`, error);
      throw error;
    }
  }

  /**
   * Tính tổng trọng lượng đơn hàng
   */
  private calculateTotalWeight(order: UserOrder, products: UserProduct[]): number {
    const orderProducts = order.productInfo?.products || [];
    
    return orderProducts.reduce((totalWeight: number, orderProduct: any) => {
      const product = products.find(p => p.id === orderProduct.productId);
      
      if (!product) return totalWeight;

      const productWeight = product.shipmentConfig?.weightGram 
        ? product.shipmentConfig.weightGram / 1000 // Chuyển từ gram sang kg
        : 0.5; // Mặc định 0.5kg

      return totalWeight + (productWeight * (orderProduct.quantity || 1));
    }, 0);
  }
}
