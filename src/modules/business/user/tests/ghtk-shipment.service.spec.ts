import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { GHTKShipmentService } from '../services/ghtk-shipment.service';
import { GHTK_TEST_CONFIG } from '../../constants/ghtk.constants';

describe('GHTKShipmentService', () => {
  let service: GHTKShipmentService;
  let configService: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GHTKShipmentService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              const config = {
                'ghtk.token': GHTK_TEST_CONFIG.TOKEN,
                'ghtk.partnerCode': GHTK_TEST_CONFIG.PARTNER_CODE,
                'ghtk.baseUrl': GHTK_TEST_CONFIG.BASE_URL,
              };
              return config[key];
            }),
          },
        },
      ],
    }).compile();

    service = module.get<GHTKShipmentService>(GHTKShipmentService);
    configService = module.get<ConfigService>(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should have correct default config', () => {
    const config = service.getConfig();
    expect(config.token).toBe(GHTK_TEST_CONFIG.TOKEN);
    expect(config.partnerCode).toBe(GHTK_TEST_CONFIG.PARTNER_CODE);
    expect(config.baseUrl).toBe(GHTK_TEST_CONFIG.BASE_URL);
    expect(config.isTestMode).toBe(true);
  });

  it('should update config correctly', () => {
    const newConfig = {
      token: 'new-token',
      partnerCode: 'new-partner-code',
      baseUrl: 'https://new-api.ghtk.vn',
      isTestMode: false,
    };

    service.setConfig(newConfig);
    const updatedConfig = service.getConfig();

    expect(updatedConfig.token).toBe(newConfig.token);
    expect(updatedConfig.partnerCode).toBe(newConfig.partnerCode);
    expect(updatedConfig.baseUrl).toBe(newConfig.baseUrl);
    expect(updatedConfig.isTestMode).toBe(newConfig.isTestMode);
  });

  describe('API Methods', () => {
    // Mock axios để test các method API
    beforeEach(() => {
      // Setup mock axios responses
      jest.spyOn(service as any, 'httpClient').mockImplementation(() => ({
        get: jest.fn(),
        post: jest.fn(),
      }));
    });

    it('should have all required API methods', () => {
      expect(typeof service.getSolutions).toBe('function');
      expect(typeof service.createOrder).toBe('function');
      expect(typeof service.calculateFee).toBe('function');
      expect(typeof service.getOrderStatus).toBe('function');
      expect(typeof service.cancelOrder).toBe('function');
      expect(typeof service.printLabel).toBe('function');
      expect(typeof service.getPickupAddresses).toBe('function');
      expect(typeof service.getLevel4Address).toBe('function');
      expect(typeof service.searchProducts).toBe('function');
      expect(typeof service.handleWebhook).toBe('function');
    });
  });

  describe('Sample Data for Testing', () => {
    it('should provide sample order data', () => {
      const sampleOrder = {
        products: [
          {
            name: 'Laptop Test',
            weight: 2.5,
            quantity: 1,
            price: 8000000,
          },
        ],
        order: {
          id: 'TEST_ORDER_123',
          pick_name: 'Kho Test',
          pick_address: '123 Test Street',
          pick_province: 'Hà Nội',
          pick_district: 'Quận Hai Bà Trưng',
          pick_ward: 'Phường Bách Khoa',
          pick_tel: '0911222333',
          name: 'Nguyễn Test',
          address: '456 Test Avenue',
          province: 'Hà Nội',
          district: 'Quận Cầu Giấy',
          ward: 'Phường Dịch Vọng',
          tel: '0987654321',
          value: 8000000,
        },
      };

      expect(sampleOrder.products).toHaveLength(1);
      expect(sampleOrder.order.id).toBe('TEST_ORDER_123');
      expect(sampleOrder.order.value).toBe(8000000);
    });

    it('should provide sample fee calculation data', () => {
      const sampleFeeRequest = {
        pickProvince: 'Hà Nội',
        pickDistrict: 'Quận Hai Bà Trưng',
        province: 'Hà Nội',
        district: 'Quận Cầu Giấy',
        weight: 1000,
        deliverOption: 'none',
      };

      expect(sampleFeeRequest.weight).toBe(1000);
      expect(sampleFeeRequest.deliverOption).toBe('none');
    });

    it('should provide sample webhook data', () => {
      const sampleWebhook = {
        partner_id: 'TEST_ORDER_123',
        label_id: 'S1.A1.TEST123',
        status_id: 5,
        action_time: '2024-01-15T12:00:00+07:00',
        reason_code: '',
        reason: '',
        weight: 2.5,
        fee: 30000,
        pick_money: 8000000,
        return_part_package: 0,
      };

      expect(sampleWebhook.status_id).toBe(5);
      expect(sampleWebhook.fee).toBe(30000);
    });
  });
});

/**
 * Integration Test Examples
 * 
 * Để test thực tế với GHTK API, uncomment và chạy các test sau:
 * Lưu ý: Cần có kết nối internet và token GHTK hợp lệ
 */

/*
describe('GHTKShipmentService Integration Tests', () => {
  let service: GHTKShipmentService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GHTKShipmentService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(() => null),
          },
        },
      ],
    }).compile();

    service = module.get<GHTKShipmentService>(GHTKShipmentService);
  });

  it('should get solutions from GHTK API', async () => {
    const result = await service.getSolutions();
    expect(result.success).toBe(true);
    expect(Array.isArray(result.data)).toBe(true);
  }, 10000);

  it('should calculate shipping fee', async () => {
    const feeRequest = {
      pickProvince: 'Hà Nội',
      pickDistrict: 'Quận Hai Bà Trưng',
      province: 'Hà Nội',
      district: 'Quận Cầu Giấy',
      weight: 1000,
      deliverOption: 'none',
    };

    const result = await service.calculateFee(feeRequest);
    expect(result.success).toBe(true);
    expect(typeof result.fee.fee).toBe('number');
    expect(result.fee.delivery).toBe(true);
  }, 10000);

  it('should get pickup addresses', async () => {
    const result = await service.getPickupAddresses();
    expect(result.success).toBe(true);
    expect(Array.isArray(result.data)).toBe(true);
  }, 10000);
});
*/
